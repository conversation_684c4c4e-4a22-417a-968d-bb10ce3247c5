{"name": "shadcn-components-test", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "biome check .", "lint:fix": "biome check . --write", "format": "biome format . --write", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "chromatic --exit-zero-on-changes"}, "dependencies": {"@betswirl/sdk-core": "^0.1.6", "@betswirl/wagmi-provider": "^0.1.6", "@coinbase/onchainkit": "^0.38.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@storybook/addon-docs": "^9.0.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "decimal.js": "^10.5.0", "lucide-react": "^0.514.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "viem": "2.31.0", "wagmi": "^2.15.6"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@chromatic-com/storybook": "^4.0.0", "@storybook/react": "9.0.8", "@storybook/react-vite": "9.0.8", "@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "chromatic": "^12.2.0", "globals": "^16.2.0", "playwright": "^1.53.0", "storybook": "9.0.8", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "vite": "^6.3.5", "vitest": "^3.2.3"}}